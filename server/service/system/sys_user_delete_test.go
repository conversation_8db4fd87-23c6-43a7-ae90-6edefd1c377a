package system

import (
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// TestDeleteUserCascade 测试删除用户时的级联操作
func TestDeleteUserCascade(t *testing.T) {
	// 跳过测试如果数据库未初始化
	if global.GVA_DB == nil {
		t.Skip("数据库未初始化，跳过测试")
	}

	// 开始事务，测试结束后回滚
	tx := global.GVA_DB.Begin()
	defer tx.Rollback()

	// 创建测试用户
	testUser := system.SysUser{
		UUID:        uuid.New(),
		Username:    "test_delete_user",
		Password:    "test_password",
		NickName:    "测试删除用户",
		AuthorityId: 888,
		Phone:       "13800138000",
		Email:       "<EMAIL>",
	}

	err := tx.Create(&testUser).Error
	require.NoError(t, err, "创建测试用户失败")

	// 创建用户权限关联
	userAuthority := system.SysUserAuthority{
		SysUserId:               testUser.ID,
		SysAuthorityAuthorityId: 888,
	}
	err = tx.Create(&userAuthority).Error
	require.NoError(t, err, "创建用户权限关联失败")

	// 创建测试设备
	testDevice := device.Device{
		DeviceID:   "test_device_001",
		DeviceName: "测试设备",
		UserID:     &testUser.ID,
		OSName:     "Windows",
		OSVersion:  "10",
	}
	err = tx.Create(&testDevice).Error
	require.NoError(t, err, "创建测试设备失败")

	// 创建认证提供商记录
	authProvider := system.SysAuthProvider{
		UserId:     testUser.ID,
		Provider:   "username",
		ProviderId: "test_delete_user",
		Username:   "test_delete_user",
		Verified:   true,
	}
	err = tx.Create(&authProvider).Error
	require.NoError(t, err, "创建认证提供商记录失败")

	// 验证数据已创建
	var userCount int64
	tx.Model(&system.SysUser{}).Where("id = ?", testUser.ID).Count(&userCount)
	assert.Equal(t, int64(1), userCount, "用户应该存在")

	var deviceCount int64
	tx.Model(&device.Device{}).Where("user_id = ?", testUser.ID).Count(&deviceCount)
	assert.Equal(t, int64(1), deviceCount, "设备应该关联到用户")

	var authProviderCount int64
	tx.Model(&system.SysAuthProvider{}).Where("user_id = ?", testUser.ID).Count(&authProviderCount)
	assert.Equal(t, int64(1), authProviderCount, "认证提供商记录应该存在")

	var userAuthorityCount int64
	tx.Model(&system.SysUserAuthority{}).Where("sys_user_id = ?", testUser.ID).Count(&userAuthorityCount)
	assert.Equal(t, int64(1), userAuthorityCount, "用户权限关联应该存在")

	// 创建用户服务实例并使用事务
	userService := &UserService{}
	
	// 模拟删除用户操作（使用事务）
	err = tx.Transaction(func(innerTx *gorm.DB) error {
		// 1. 将关联的设备的userId置空
		if err := innerTx.Model(&device.Device{}).Where("user_id = ?", testUser.ID).Update("user_id", nil).Error; err != nil {
			return err
		}

		// 2. 删除相关的认证提供商记录
		if err := innerTx.Delete(&[]system.SysAuthProvider{}, "user_id = ?", testUser.ID).Error; err != nil {
			return err
		}

		// 3. 删除用户权限关联
		if err := innerTx.Delete(&[]system.SysUserAuthority{}, "sys_user_id = ?", testUser.ID).Error; err != nil {
			return err
		}

		// 4. 删除用户记录
		if err := innerTx.Where("id = ?", testUser.ID).Delete(&system.SysUser{}).Error; err != nil {
			return err
		}

		return nil
	})
	require.NoError(t, err, "删除用户操作失败")

	// 验证用户已删除
	tx.Model(&system.SysUser{}).Where("id = ?", testUser.ID).Count(&userCount)
	assert.Equal(t, int64(0), userCount, "用户应该已被删除")

	// 验证设备的userId已置空
	var updatedDevice device.Device
	err = tx.Where("device_id = ?", testDevice.DeviceID).First(&updatedDevice).Error
	require.NoError(t, err, "查询设备失败")
	assert.Nil(t, updatedDevice.UserID, "设备的userId应该已置空")

	// 验证认证提供商记录已删除
	tx.Model(&system.SysAuthProvider{}).Where("user_id = ?", testUser.ID).Count(&authProviderCount)
	assert.Equal(t, int64(0), authProviderCount, "认证提供商记录应该已删除")

	// 验证用户权限关联已删除
	tx.Model(&system.SysUserAuthority{}).Where("sys_user_id = ?", testUser.ID).Count(&userAuthorityCount)
	assert.Equal(t, int64(0), userAuthorityCount, "用户权限关联应该已删除")
}

// TestDeleteUserWithMultipleDevices 测试删除用户时处理多个设备
func TestDeleteUserWithMultipleDevices(t *testing.T) {
	// 跳过测试如果数据库未初始化
	if global.GVA_DB == nil {
		t.Skip("数据库未初始化，跳过测试")
	}

	// 开始事务，测试结束后回滚
	tx := global.GVA_DB.Begin()
	defer tx.Rollback()

	// 创建测试用户
	testUser := system.SysUser{
		UUID:        uuid.New(),
		Username:    "test_multi_devices_user",
		Password:    "test_password",
		NickName:    "测试多设备用户",
		AuthorityId: 888,
		Phone:       "13800138001",
		Email:       "<EMAIL>",
	}

	err := tx.Create(&testUser).Error
	require.NoError(t, err, "创建测试用户失败")

	// 创建多个测试设备
	devices := []device.Device{
		{
			DeviceID:   "test_device_multi_001",
			DeviceName: "测试设备1",
			UserID:     &testUser.ID,
			OSName:     "Windows",
			OSVersion:  "10",
		},
		{
			DeviceID:   "test_device_multi_002",
			DeviceName: "测试设备2",
			UserID:     &testUser.ID,
			OSName:     "macOS",
			OSVersion:  "12",
		},
		{
			DeviceID:   "test_device_multi_003",
			DeviceName: "测试设备3",
			UserID:     &testUser.ID,
			OSName:     "Linux",
			OSVersion:  "Ubuntu 20.04",
		},
	}

	for _, dev := range devices {
		err = tx.Create(&dev).Error
		require.NoError(t, err, "创建测试设备失败")
	}

	// 验证设备已创建并关联到用户
	var deviceCount int64
	tx.Model(&device.Device{}).Where("user_id = ?", testUser.ID).Count(&deviceCount)
	assert.Equal(t, int64(3), deviceCount, "应该有3个设备关联到用户")

	// 执行删除用户操作（只处理设备部分）
	err = tx.Model(&device.Device{}).Where("user_id = ?", testUser.ID).Update("user_id", nil).Error
	require.NoError(t, err, "置空设备userId失败")

	// 验证所有设备的userId都已置空
	var devicesWithUser int64
	tx.Model(&device.Device{}).Where("user_id = ?", testUser.ID).Count(&devicesWithUser)
	assert.Equal(t, int64(0), devicesWithUser, "所有设备的userId应该已置空")

	// 验证设备记录仍然存在
	var totalDevices int64
	tx.Model(&device.Device{}).Where("device_id IN ?", []string{
		"test_device_multi_001",
		"test_device_multi_002", 
		"test_device_multi_003",
	}).Count(&totalDevices)
	assert.Equal(t, int64(3), totalDevices, "设备记录应该仍然存在")
}

// TestDeleteUserWithMultipleAuthProviders 测试删除用户时处理多个认证提供商
func TestDeleteUserWithMultipleAuthProviders(t *testing.T) {
	// 跳过测试如果数据库未初始化
	if global.GVA_DB == nil {
		t.Skip("数据库未初始化，跳过测试")
	}

	// 开始事务，测试结束后回滚
	tx := global.GVA_DB.Begin()
	defer tx.Rollback()

	// 创建测试用户
	testUser := system.SysUser{
		UUID:        uuid.New(),
		Username:    "test_multi_auth_user",
		Password:    "test_password",
		NickName:    "测试多认证用户",
		AuthorityId: 888,
		Phone:       "13800138002",
		Email:       "<EMAIL>",
	}

	err := tx.Create(&testUser).Error
	require.NoError(t, err, "创建测试用户失败")

	// 创建多个认证提供商记录
	authProviders := []system.SysAuthProvider{
		{
			UserId:     testUser.ID,
			Provider:   "username",
			ProviderId: "test_multi_auth_user",
			Username:   "test_multi_auth_user",
			Verified:   true,
		},
		{
			UserId:     testUser.ID,
			Provider:   "email",
			ProviderId: "<EMAIL>",
			Email:      "<EMAIL>",
			Verified:   true,
		},
		{
			UserId:     testUser.ID,
			Provider:   "phone",
			ProviderId: "13800138002",
			Phone:      "13800138002",
			Verified:   true,
		},
	}

	for _, provider := range authProviders {
		err = tx.Create(&provider).Error
		require.NoError(t, err, "创建认证提供商记录失败")
	}

	// 验证认证提供商记录已创建
	var authProviderCount int64
	tx.Model(&system.SysAuthProvider{}).Where("user_id = ?", testUser.ID).Count(&authProviderCount)
	assert.Equal(t, int64(3), authProviderCount, "应该有3个认证提供商记录")

	// 执行删除认证提供商操作
	err = tx.Delete(&[]system.SysAuthProvider{}, "user_id = ?", testUser.ID).Error
	require.NoError(t, err, "删除认证提供商记录失败")

	// 验证所有认证提供商记录都已删除
	tx.Model(&system.SysAuthProvider{}).Where("user_id = ?", testUser.ID).Count(&authProviderCount)
	assert.Equal(t, int64(0), authProviderCount, "所有认证提供商记录应该已删除")
}
